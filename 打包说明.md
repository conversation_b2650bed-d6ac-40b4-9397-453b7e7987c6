# PDF合同重命名工具 - 打包说明

## 项目概述

这是一个基于Python的PDF合同重命名工具，使用EasyOCR进行文字识别，支持GPU加速，可以自动识别PDF文件中的合同编号并重命名文件。

## 环境要求

### Python版本
- Python 3.8 或更高版本

### 必要依赖
- easyocr>=1.7.0 (OCR文字识别)
- PyMuPDF>=1.23.0 (PDF处理)
- Pillow>=9.0.0 (图像处理)
- opencv-python-headless>=4.8.0 (图像处理)
- torch>=1.9.0 (深度学习框架)
- torchvision>=0.10.0 (计算机视觉)
- pyinstaller>=6.0.0 (打包工具)

### 可选依赖 (GPU支持)
- CUDA 11.8 或更高版本
- 对应的PyTorch CUDA版本

## 安装步骤

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 验证安装
```bash
python -c "import easyocr, torch, cv2, fitz, PIL; print('所有依赖已正确安装')"
```

### 3. 检查GPU支持 (可选)
```bash
python -c "import torch; print('CUDA可用:', torch.cuda.is_available())"
```

## 打包步骤

### Windows系统
1. 双击运行 `build_exe.bat`
2. 等待打包完成
3. 在 `release` 文件夹中找到可执行文件

### Linux/Mac系统
1. 在终端中运行: `./build_exe.sh`
2. 等待打包完成
3. 在 `release` 文件夹中找到可执行文件

### 手动打包
如果自动脚本失败，可以手动执行：
```bash
pyinstaller pdf_renamer.spec
```

## 打包文件说明

### 核心文件
- `pdf_renamer.py` - 主程序文件
- `pdf_renamer.spec` - PyInstaller配置文件
- `requirements.txt` - 依赖列表
- `build_exe.bat` - Windows打包脚本
- `build_exe.sh` - Linux/Mac打包脚本

### 配置文件特点
- 包含所有EasyOCR必要的数据文件
- 包含PyTorch和相关依赖
- 自动处理隐藏导入
- 排除不必要的大型库以减小文件大小
- 支持图标设置

## 常见问题解决

### 1. EasyOCR初始化失败
- 确保网络连接正常（首次运行需要下载模型）
- 检查是否有足够的磁盘空间
- 尝试手动安装：`pip install easyocr --upgrade`

### 2. GPU不可用
- 检查CUDA安装：`nvidia-smi`
- 安装CUDA版本的PyTorch：`pip install torch torchvision --index-url https://download.pytorch.org/whl/cu118`
- 在程序中取消勾选GPU选项

### 3. 打包后运行失败
- 检查目标机器是否有Visual C++ Redistributable
- 确保所有依赖文件都被正确包含
- 查看错误日志，通常在临时目录中

### 4. 文件过大
- 可以在spec文件中添加更多排除项
- 使用UPX压缩（已在配置中启用）
- 考虑分离某些大型依赖

## 性能优化建议

### GPU加速
- 如果有NVIDIA GPU，启用GPU选项可显著提升OCR速度
- 确保安装了正确的CUDA版本
- 首次使用GPU时可能需要额外的初始化时间

### 内存优化
- 处理大量文件时建议分批处理
- 定期清理临时文件夹
- 监控内存使用情况

## 部署注意事项

1. **首次运行**: EasyOCR会自动下载模型文件（约100MB），需要网络连接
2. **权限要求**: 需要读写PDF文件夹和临时文件夹的权限
3. **系统兼容性**: 支持Windows 10+, Linux, macOS
4. **防病毒软件**: 某些防病毒软件可能误报，需要添加白名单

## 更新和维护

### 更新依赖
```bash
pip install -r requirements.txt --upgrade
```

### 重新打包
修改代码后需要重新运行打包脚本

### 版本管理
建议在spec文件中更新版本信息，便于追踪
