# PDF重命名工具打包问题解决方案

## 问题分析

您遇到的错误是典型的PyInstaller打包问题：

```
ModuleNotFoundError: No module named 'scipy._cyutility'
```

这个错误的根本原因是：
1. EasyOCR依赖scipy库
2. scipy包含许多Cython编译的扩展模块
3. PyInstaller在自动分析依赖时遗漏了这些动态加载的模块

## 解决方案

### 1. 代码修改
已在`pdf_renamer.py`中添加了隐藏导入，确保关键的scipy模块被包含：

```python
# 为PyInstaller添加隐藏导入
try:
    import scipy._lib._ccallback_c
    import scipy._lib._fpumode
    import scipy.sparse.csgraph._validation
    # ... 更多模块
except ImportError:
    pass  # 在开发环境中这些模块可能不存在
```

### 2. 更新spec文件
已更新`pdf_renamer.spec`文件，添加了完整的scipy依赖：

- 添加了所有必要的scipy子模块
- 使用`collect_submodules('scipy')`自动收集所有scipy模块
- 包含了EasyOCR需要的所有依赖

### 3. 打包步骤

#### 方法一：使用批处理文件（推荐）
```bash
# 运行批处理文件
build.bat
```

#### 方法二：手动打包
```bash
# 清理之前的构建
rmdir /s /q build dist __pycache__

# 使用spec文件打包
pyinstaller pdf_renamer.spec
```

### 4. 验证依赖安装

确保所有依赖都正确安装：
```bash
pip install -r requirements.txt
```

### 5. 可能的额外解决方案

如果问题仍然存在，可以尝试：

#### 选项A：使用--collect-all参数
```bash
pyinstaller --collect-all scipy --collect-all easyocr pdf_renamer.py
```

#### 选项B：手动复制缺失的模块
在打包后，如果仍有模块缺失，可以手动将缺失的.pyd文件复制到dist目录。

#### 选项C：使用虚拟环境
```bash
# 创建新的虚拟环境
python -m venv venv_package
venv_package\Scripts\activate

# 只安装必要的依赖
pip install -r requirements.txt

# 在干净环境中打包
pyinstaller pdf_renamer.spec
```

## 测试建议

打包完成后，建议进行以下测试：

1. **基本功能测试**：确保程序能正常启动
2. **OCR功能测试**：测试EasyOCR是否能正常工作
3. **GPU功能测试**：如果启用了GPU，测试CUDA是否正常
4. **文件处理测试**：使用实际PDF文件测试完整流程

## 常见问题

### Q: 打包后文件很大
A: 这是正常的，因为包含了完整的scipy、torch等库。可以考虑：
- 使用`--exclude-module`排除不需要的模块
- 使用UPX压缩（已在spec中启用）

### Q: 启动很慢
A: 第一次启动时EasyOCR需要下载模型文件，这是正常的。

### Q: 仍然有模块缺失错误
A: 可以在spec文件的hiddenimports中添加缺失的模块名。

## 注意事项

1. 确保在打包环境中所有依赖都能正常工作
2. 建议在目标运行环境中测试打包后的程序
3. 如果使用GPU功能，确保目标机器有相应的CUDA环境
