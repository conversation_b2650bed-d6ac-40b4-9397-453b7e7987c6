@echo off
echo ========================================
echo PDF合同重命名工具 - 自动打包脚本
echo ========================================
echo.

echo 1. 检查Python环境...
python --version
if %errorlevel% neq 0 (
    echo 错误: Python未安装或未添加到PATH
    pause
    exit /b 1
)

echo.
echo 2. 检查必要的包...
python -c "import easyocr, torch, cv2, fitz, PIL; print('所有必要的包已安装')"
if %errorlevel% neq 0 (
    echo 错误: 缺少必要的包，请运行: pip install -r requirements.txt
    pause
    exit /b 1
)

echo.
echo 3. 清理之前的构建文件...
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"
if exist "__pycache__" rmdir /s /q "__pycache__"

echo.
echo 4. 开始打包...
echo 这可能需要几分钟时间，请耐心等待...
pyinstaller pdf_renamer.spec

if %errorlevel% neq 0 (
    echo.
    echo 错误: 打包失败
    pause
    exit /b 1
)

echo.
echo 5. 检查打包结果...
if exist "dist\PDF合同重命名工具.exe" (
    echo 成功! 可执行文件已生成: dist\PDF合同重命名工具.exe
    echo.
    echo 6. 创建发布文件夹...
    if not exist "release" mkdir "release"
    copy "dist\PDF合同重命名工具.exe" "release\"
    if exist "icon.ico" copy "icon.ico" "release\"
    
    echo.
    echo 打包完成! 文件位置:
    echo - 可执行文件: release\PDF合同重命名工具.exe
    echo - 完整版本: dist\PDF合同重命名工具.exe
    echo.
    echo 注意事项:
    echo - 首次运行时EasyOCR会下载模型文件，需要网络连接
    echo - 如果启用GPU，确保目标机器有CUDA支持
    echo - 建议在目标机器上测试所有功能
) else (
    echo 错误: 未找到生成的可执行文件
    exit /b 1
)

echo.
echo 按任意键退出...
pause >nul
