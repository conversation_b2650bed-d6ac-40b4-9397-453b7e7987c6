@echo off
echo 开始打包PDF重命名工具...
echo.

REM 清理之前的构建文件
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"
if exist "__pycache__" rmdir /s /q "__pycache__"

echo 清理完成，开始打包...
echo.

REM 使用spec文件进行打包
pyinstaller pdf_renamer.spec

if %errorlevel% equ 0 (
    echo.
    echo 打包成功！
    echo 可执行文件位置: dist\PDF合同重命名工具.exe
    echo.
    pause
) else (
    echo.
    echo 打包失败！请检查错误信息。
    echo.
    pause
)
