# 导入必要的库
import os  # 操作系统接口
import re  # 正则表达式
import tkinter as tk  # GUI界面库
from queue import Queue  # 线程安全队列
from tkinter import filedialog, messagebox  # 文件对话框和消息框
from PIL import ImageTk  # PIL图像转Tkinter图像
from pdf2image import convert_from_path  # PDF转图像
from PIL import Image  # 图像处理
import pytesseract  # OCR识别
import threading  # 多线程
import subprocess  # 子进程管理

class PDFRenamerGUI:
    """PDF重命名工具的主GUI类"""
    
    def __init__(self, root):
        """初始化GUI界面"""
        self.root = root
        self.root.title("合同文件重命名工具")
        
        # 设置主窗口图标
        self.set_window_icon(self.root)
        
        # 创建日志窗口
        self.log_window = tk.Toplevel(self.root)
        self.log_window.title("处理日志")
        self.log_window.geometry("600x400")
        self.log_window.protocol("WM_DELETE_WINDOW", lambda: None)  # 禁用关闭按钮
        self.set_window_icon(self.log_window)
        
        # 创建带滚动条的日志文本部件
        self.log_text = tk.Text(self.log_window, wrap=tk.WORD)
        scrollbar = tk.Scrollbar(self.log_window, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        # 线程安全的日志队列
        self.log_queue = Queue()
        self.root.after(100, self.process_log_queue)  # 每100ms处理一次日志队列
        
        # 布局滚动条和日志文本
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 默认值设置
        self.pdf_folder = tk.StringVar(value="pdf_files")  # PDF文件夹路径
        self.temp_folder = tk.StringVar(value="temp_images")  # 临时文件夹路径
        self.crop_area = (0, 0, 2338, 1654)  # 默认裁剪区域 (左,上,右,下)
        self.tesseract_cmd = tk.StringVar(value=r'Tesseract-OCR\tesseract.exe')  # Tesseract路径
        self.poppler_path = tk.StringVar(value=r'poppler-24.08.0\Library\bin')  # Poppler路径
        self.regex_pattern = tk.StringVar(value=r'(?:MO|MPSO)\d{11}')  # 默认合同编号正则表达式
        self.selection_start = None  # 区域选择起始坐标
        self.selection_rect = None  # 区域选择矩形对象
        
        # Create GUI elements
        tk.Label(root, text="合同文件夹:").grid(row=0, column=0, sticky="w")
        tk.Entry(root, textvariable=self.pdf_folder, width=40).grid(row=0, column=1)
        tk.Button(root, text="浏览", command=self.browse_pdf_folder).grid(row=0, column=2)
        
        tk.Label(root, text="TEMP文件夹:").grid(row=1, column=0, sticky="w")
        tk.Entry(root, textvariable=self.temp_folder, width=40).grid(row=1, column=1)
        tk.Button(root, text="浏览", command=self.browse_temp_folder).grid(row=1, column=2)
        
        tk.Label(root, text="Tesseract路径:").grid(row=2, column=0, sticky="w")
        tk.Entry(root, textvariable=self.tesseract_cmd, width=40).grid(row=2, column=1)
        tk.Button(root, text="浏览", command=self.browse_tesseract).grid(row=2, column=2)
        
        tk.Label(root, text="Poppler路径:").grid(row=3, column=0, sticky="w")
        tk.Entry(root, textvariable=self.poppler_path, width=40).grid(row=3, column=1)
        tk.Button(root, text="浏览", command=self.browse_poppler).grid(row=3, column=2)
        
        tk.Label(root, text="正则表达式:").grid(row=4, column=0, sticky="w")
        tk.Entry(root, textvariable=self.regex_pattern, width=40).grid(row=4, column=1)
        
        self.process_btn = tk.Button(root, text="处理PDF文件", command=self.start_processing, bg="green", fg="white")
        self.process_btn.grid(row=5, column=2, pady=5)
    
    def set_window_icon(self, window):
        """为指定窗口设置图标"""
        icon_paths = ['icon.ico', 'icon.png', 'icon.jpg']  # 可能的图标文件路径
        for icon_path in icon_paths:
            if os.path.exists(icon_path):
                try:
                    window.iconbitmap(icon_path)  # 尝试设置.ico图标
                    break
                except:
                    try:
                        img = tk.PhotoImage(file=icon_path)  # 尝试加载其他格式图片
                        window.tk.call('wm', 'iconphoto', window._w, img)
                        break
                    except:
                        continue  # 如果都不支持则继续尝试下一个
    
    def process_log_queue(self):
        """处理日志队列中的消息"""
        while not self.log_queue.empty():
            try:
                message = self.log_queue.get_nowait()  # 非阻塞获取消息
                self.log_text.insert(tk.END, message + "\n")  # 插入日志
                self.log_text.see(tk.END)  # 滚动到最新
                self.log_text.update()  # 更新显示
            except:
                break  # 队列为空时退出
        self.root.after(100, self.process_log_queue)  # 100ms后再次检查
    
    def log_message(self, message):
        """通过线程安全队列添加日志消息"""
        self.log_queue.put(message)  # 线程安全地添加消息
    
    
    def browse_pdf_folder(self):
        """浏览并选择PDF文件夹"""
        folder = filedialog.askdirectory()  # 打开文件夹选择对话框
        if folder:
            self.pdf_folder.set(folder)  # 设置选择的文件夹路径
    
    def browse_temp_folder(self):
        """浏览并选择临时文件夹"""
        folder = filedialog.askdirectory()  # 打开文件夹选择对话框
        if folder:
            self.temp_folder.set(folder)  # 设置选择的临时文件夹路径
    
    def browse_tesseract(self):
        """浏览并选择Tesseract可执行文件"""
        file = filedialog.askopenfilename(filetypes=[("Executable", "*.exe")])  # 只显示.exe文件
        if file:
            self.tesseract_cmd.set(file)  # 设置Tesseract路径
    
    def browse_poppler(self):
        """浏览并选择Poppler路径"""
        folder = filedialog.askdirectory()  # 打开文件夹选择对话框
        if folder:
            self.poppler_path.set(folder)  # 设置Poppler路径
    
    def start_processing(self):
        """开始PDF处理流程，首先显示第一个PDF用于区域选择"""
        pdf_folder = self.pdf_folder.get()  # 获取PDF文件夹路径
        if not os.path.exists(pdf_folder) or not os.listdir(pdf_folder):
            messagebox.showerror("错误", "PDF文件夹为空或不存在!")  # 错误提示
            return
            
        # 查找第一个PDF文件
        for filename in os.listdir(pdf_folder):
            if filename.lower().endswith('.pdf'):
                pdf_path = os.path.join(pdf_folder, filename)
                break
        else:
            messagebox.showerror("错误", "没有找到PDF文件!")  # 错误提示
            return
            
        # 将第一页转换为图像
        try:
            images = convert_from_path(pdf_path, poppler_path=self.poppler_path.get())
            if not images:
                messagebox.showerror("错误", "无法读取PDF文件!")  # 错误提示
                return
                
            # 显示选择窗口并传入第一页图像
            if hasattr(self, 'is_retry'):
                del self.is_retry  # 清除重试标志(如果是初始选择)
            self.show_selection_window(images[0])  # 显示选择窗口
            
        except Exception as e:
            messagebox.showerror("错误", f"无法显示PDF: {str(e)}")  # 错误提示
            self.process_btn.config(state=tk.NORMAL)  # 恢复按钮状态
    
    def on_selection_start(self, event):
        """开始区域选择(鼠标按下事件)"""
        self.selection_start = (event.x, event.y)  # 记录起始坐标
        if self.selection_rect:
            self.canvas.delete(self.selection_rect)  # 清除之前的选区
    
    def on_selection_drag(self, event):
        """更新选择区域(鼠标拖动事件)"""
        if not self.selection_start:
            return  # 如果没有起始点则返回
            
        if self.selection_rect:
            self.canvas.delete(self.selection_rect)  # 删除旧的选区
            
        x0, y0 = self.selection_start  # 起始坐标
        x1, y1 = event.x, event.y  # 当前坐标
        # 创建红色矩形选区
        self.selection_rect = self.canvas.create_rectangle(x0, y0, x1, y1,
                                                         outline='red', width=2)
    
    def on_selection_end(self, event):
        """完成选择(鼠标释放事件)"""
        if not self.selection_start:
            return  # 如果没有起始点则返回
            
        x0, y0 = self.selection_start  # 起始坐标
        x1, y1 = event.x, event.y  # 结束坐标
        # 将缩放后的坐标转换回原始图像坐标
        orig_x0 = int(x0 / self.scale_factor)
        orig_y0 = int(y0 / self.scale_factor)
        orig_x1 = int(x1 / self.scale_factor)
        orig_y1 = int(y1 / self.scale_factor)
        # 设置裁剪区域(确保左上和右下坐标正确)
        self.crop_area = (min(orig_x0, orig_x1), min(orig_y0, orig_y1),
                         max(orig_x0, orig_x1), max(orig_y0, orig_y1))
    
    def confirm_selection(self):
        """确认选择的区域"""
        if not hasattr(self, 'crop_area') or len(self.crop_area) != 4:
            messagebox.showerror("错误", "请先选择合同编号区域!")  # 错误提示
            return
            
        self.selection_window.destroy()  # 关闭选择窗口
        
        # 只有初始选择时才启动处理流程(不是重试)
        if not hasattr(self, 'is_retry'):
            self.process_btn.config(state=tk.DISABLED, text="处理中...")  # 禁用按钮
            thread = threading.Thread(target=self.process_pdfs)  # 创建处理线程
            thread.start()  # 启动线程
        
    def process_pdfs(self):
        """处理所有PDF文件的主方法"""
        try:
            # 从GUI输入设置全局变量
            PDF_FOLDER = self.pdf_folder.get()  # 获取PDF文件夹路径
            TEMP_FOLDER = self.temp_folder.get()  # 获取临时文件夹路径
            pytesseract.pytesseract.tesseract_cmd = self.tesseract_cmd.get()  # 设置Tesseract路径
            
            # 处理每个PDF文件
            for filename in os.listdir(PDF_FOLDER):
                if not filename.lower().endswith('.pdf'):
                    continue  # 跳过非PDF文件
                    
                pdf_path = os.path.join(PDF_FOLDER, filename)
                while True:  # 当前文件的重试循环
                    try:
                        # 使用当前裁剪区域处理
                        code = self.process_single_pdf(pdf_path, TEMP_FOLDER)
                        
                        if code:
                            # 如果提取到编号则重命名文件
                            new_name = f"{code}.pdf"
                            new_path = os.path.join(PDF_FOLDER, new_name)
                            os.rename(pdf_path, new_path)
                            self.log_message(f"重命名 {filename} 为 {new_name}")
                            break
                        else:
                            # 询问用户如何处理识别失败
                            choice = self.handle_failed_recognition(pdf_path)
                            if choice == "retry":
                                continue  # 重新尝试
                            elif choice == "skip":
                                self.log_message(f"跳过 {filename}")
                                break  # 跳过当前文件
                            
                    except Exception as e:
                        # 显示错误信息
                        self.root.after(0, lambda f=filename, e=e: messagebox.showerror("错误", f"处理{f}时发生错误: {str(e)}"))
                        break
                        
            # 处理完成提示
            self.root.after(0, lambda: messagebox.showinfo("完成", "PDF文件处理完成!"))
        except Exception as e:
            # 显示全局错误
            self.root.after(0, lambda e=e: messagebox.showerror("错误", f"发生错误: {str(e)}"))
        finally:
            # 恢复按钮状态
            self.root.after(0, lambda: self.process_btn.config(state=tk.NORMAL, text="处理PDF文件"))
    
    def process_single_pdf(self, pdf_path, temp_folder):
        """处理单个PDF文件并返回合同编号"""
        # 将PDF转换为图像
        images = convert_from_path(pdf_path, poppler_path=self.poppler_path.get())
        if not images:
            return None  # 转换失败返回None
            
        # 确保临时目录存在并保存第一页为JPEG
        ensure_dir(temp_folder)
        temp_image = os.path.join(temp_folder, "temp.jpg")
        images[0].save(temp_image, 'JPEG')
        
        # 提取合同编号区域
        img = Image.open(temp_image)
        cropped = img.crop(self.crop_area)  # 根据选择的区域裁剪
        
        # 保存裁剪后的图像到临时文件
        temp_img_path = os.path.join(self.temp_folder.get(), "temp_crop.jpg")
        cropped.save(temp_img_path)
        
        # 准备Tesseract OCR命令
        tesseract_cmd = [
            self.tesseract_cmd.get(),  # Tesseract路径
            temp_img_path,  # 输入图像路径
            'stdout',  # 输出到标准输出
            '-l', 'eng',  # 使用英语语言
            '--psm', '6'  # 页面分割模式6(假设为统一文本块)
        ]
        
        # Windows下隐藏控制台窗口
        startupinfo = None
        if os.name == 'nt':  # Windows系统
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE
        
        try:
            # 运行Tesseract OCR
            result = subprocess.run(
                tesseract_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                startupinfo=startupinfo,
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            )
            text = result.stdout
            # 使用正则表达式匹配合同编号
            match = re.search(self.regex_pattern.get(), text)
            return match.group() if match else None  # 返回匹配结果或None
        except Exception as e:
            self.log_message(f"OCR错误: {str(e)}")  # 记录OCR错误
            return None
    
    def show_selection_window(self, image):
        """显示图像选择窗口"""
        self.selection_window = tk.Toplevel(self.root)
        self.selection_window.title("选择合同编号区域")
        
        # 将图像转换为Tkinter格式并缩放以适应屏幕
        self.selection_img = image
        self.original_width, self.original_height = self.selection_img.size
        
        # 计算缩放因子以适应窗口(最大800x600)
        max_width, max_height = 800, 600
        width_ratio = max_width / self.original_width
        height_ratio = max_height / self.original_height
        self.scale_factor = min(width_ratio, height_ratio)
        
        # 计算缩放后的尺寸
        scaled_width = int(self.original_width * self.scale_factor)
        scaled_height = int(self.original_height * self.scale_factor)
        
        # 创建缩放后的图像
        scaled_img = self.selection_img.resize((scaled_width, scaled_height), Image.LANCZOS)
        self.tk_img = ImageTk.PhotoImage(scaled_img)
        
        # 创建带滚动条的画布
        frame = tk.Frame(self.selection_window)
        frame.pack(fill=tk.BOTH, expand=True)
        
        canvas = tk.Canvas(frame, width=scaled_width, height=scaled_height)
        canvas.create_image(0, 0, anchor=tk.NW, image=self.tk_img)  # 显示图像
        canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 保存画布引用
        self.canvas = canvas
        
        # 绑定鼠标事件用于选择区域
        self.canvas.bind("<ButtonPress-1>", self.on_selection_start)  # 鼠标按下
        self.canvas.bind("<B1-Motion>", self.on_selection_drag)  # 鼠标拖动
        self.canvas.bind("<ButtonRelease-1>", self.on_selection_end)  # 鼠标释放
        
        # 在底部添加确认按钮
        btn_frame = tk.Frame(self.selection_window)
        btn_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=10)
        
        confirm_btn = tk.Button(btn_frame, text="确认选择",
                              command=self.confirm_selection,
                              width=15, height=2)
        confirm_btn.pack()  # 布局确认按钮
    
    def handle_failed_recognition(self, pdf_path):
        """Show dialog for failed recognition and return user choice"""
        self.is_retry = True  # Mark as retry operation
        result = {"choice": None, "done": False}
        
        def on_retry():
            nonlocal result
            result["choice"] = "retry"
            dialog.destroy()
            
            # Convert PDF to image and show selection window
            try:
                images = convert_from_path(pdf_path, poppler_path=self.poppler_path.get())
                if images:
                    self.show_selection_window(images[0])
                    # Wait for user to confirm new selection
                    self.selection_window.wait_window()
                    result["done"] = True
            except Exception as e:
                messagebox.showerror("错误", f"无法显示PDF: {str(e)}")
                result["choice"] = "skip"
                result["done"] = True
            
        def on_skip():
            nonlocal result
            result["choice"] = "skip"
            result["done"] = True
            dialog.destroy()
            
        dialog = tk.Toplevel(self.root)
        dialog.title("识别失败")
        dialog.resizable(False, False)
        
        tk.Label(dialog, text=f"无法识别文件中的合同编号:\n{os.path.basename(pdf_path)}",
               padx=20, pady=10).pack()
               
        btn_frame = tk.Frame(dialog)
        btn_frame.pack(pady=10)
        
        tk.Button(btn_frame, text="重新选择区域", command=on_retry,
                width=15).pack(side=tk.LEFT, padx=5)
        tk.Button(btn_frame, text="跳过此文件", command=on_skip,
                width=15).pack(side=tk.LEFT, padx=5)
        
        # Wait for user response
        dialog.transient(self.root)
        dialog.grab_set()
        self.root.wait_window(dialog)
        
        # Wait for selection to complete if retry was chosen
        while result["choice"] == "retry" and not result["done"]:
            self.root.update()
            
        return result["choice"]

def ensure_dir(directory):
    """确保目录存在，不存在则创建"""
    if not os.path.exists(directory):
        os.makedirs(directory)  # 递归创建目录

def extract_contract_code(image_path):
    """使用OCR和正则表达式从图片中提取合同编号"""
    try:
        # 裁剪图片到合同编号区域
        img = Image.open(image_path)
        cropped = img.crop(CROP_AREA)
        
        # 执行OCR识别
        text = pytesseract.image_to_string(cropped, lang='eng')
        
        
        # 使用正则表达式提取合同编号(例如 MO20250211001)
        match = re.search(self.regex_pattern.get(), text)
        if match:
            return match.group()
        return None
    except Exception as e:
        self.root.after(0, lambda p=image_path, e=e: self.log_message(f"Error processing {p}: {str(e)}"))
        return None

def process_pdf(pdf_path):
    """处理单个PDF文件"""
    try:
        # 将PDF转换为图片
        images = convert_from_path(pdf_path)
        
        # 只保存第一页(假设编号在第一页)
        if images:
            ensure_dir(TEMP_FOLDER)
            temp_image = os.path.join(TEMP_FOLDER, "temp.jpg")
            images[0].save(temp_image, 'JPEG')
            
            # 提取合同编号
            code = extract_contract_code(temp_image)
            if code:
                return code
    except Exception as e:
        self.root.after(0, lambda p=pdf_path, e=e: self.log_message(f"Error processing {p}: {str(e)}"))
    return None

def main(pdf_folder, temp_folder, crop_area):
    """主处理函数"""
    global PDF_FOLDER, TEMP_FOLDER, CROP_AREA
    PDF_FOLDER = pdf_folder
    TEMP_FOLDER = temp_folder
    CROP_AREA = crop_area
    
    ensure_dir(PDF_FOLDER)
    
    # 处理输入文件夹中的所有PDF文件
    for filename in os.listdir(PDF_FOLDER):
        if filename.lower().endswith('.pdf'):
            pdf_path = os.path.join(PDF_FOLDER, filename)
            code = process_pdf(pdf_path)
            
            if code:
                # 重命名文件
                new_name = f"{code}.pdf"
                new_path = os.path.join(PDF_FOLDER, new_name)
                os.rename(pdf_path, new_path)
                self.root.after(0, lambda: self.log_message(f"Renamed {filename} to {new_name}"))
            else:
                self.root.after(0, lambda: self.log_message(f"Could not extract code from {filename}"))

if __name__ == "__main__":
    root = tk.Tk()
    app = PDFRenamerGUI(root)
    root.mainloop()
