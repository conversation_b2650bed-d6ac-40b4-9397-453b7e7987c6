# Core dependencies
pdf2image>=1.16.0
Pillow>=9.0.0
easyocr>=1.7.0
PyMuPDF>=1.23.0

# GUI dependencies (tkinter is built-in, but these might be needed)
# tkinter is included with Python standard library

# Image processing and OCR dependencies
opencv-python-headless>=4.8.0
numpy>=1.21.0
torch>=1.9.0
torchvision>=0.10.0

# Additional dependencies for EasyOCR
scipy>=1.7.0
scikit-image>=0.18.0
python-bidi>=0.4.2
pyclipper>=1.3.0

# Packaging dependencies
pyinstaller>=6.0.0
pyinstaller-hooks-contrib>=2023.0

# Optional GPU support (uncomment if you have CUDA)
# torch>=1.9.0+cu118
# torchvision>=0.10.0+cu118