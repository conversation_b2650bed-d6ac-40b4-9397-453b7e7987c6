//========================================================================
//
// CurlPDFDocBuilder.h
//
// This file is licensed under the GPLv2 or later
//
// Copyright 2010 Hib Eris <<EMAIL>>
// Copyright 2010, 2018, 2022 Albert <PERSON>tals Cid <<EMAIL>>
// Copyright 2021 Oliver <PERSON> <<EMAIL>>
//
//========================================================================

#ifndef CURLPDFDOCBUILDER_H
#define CURLPDFDOCBUILDER_H

#include "PDFDocBuilder.h"

//------------------------------------------------------------------------
// CurlPDFDocBuilder
//
// The CurlPDFDocBuilder implements a PDFDocBuilder for 'http(s)://'.
//------------------------------------------------------------------------

class CurlPDFDocBuilder : public PDFDocBuilder
{

public:
    std::unique_ptr<PDFDoc> buildPDFDoc(const GooString &uri, const std::optional<GooString> &ownerPassword = {}, const std::optional<GooString> &userPassword = {}, void *guiDataA = nullptr) override;
    bool supports(const GooString &uri) override;
};

#endif /* CURLPDFDOCBUILDER_H */
