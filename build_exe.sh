#!/bin/bash

echo "========================================"
echo "PDF合同重命名工具 - 自动打包脚本"
echo "========================================"
echo

echo "1. 检查Python环境..."
python3 --version
if [ $? -ne 0 ]; then
    echo "错误: Python3未安装或未添加到PATH"
    exit 1
fi

echo
echo "2. 检查必要的包..."
python3 -c "import easyocr, torch, cv2, fitz, PIL; print('所有必要的包已安装')"
if [ $? -ne 0 ]; then
    echo "错误: 缺少必要的包，请运行: pip3 install -r requirements.txt"
    exit 1
fi

echo
echo "3. 清理之前的构建文件..."
rm -rf build dist __pycache__

echo
echo "4. 开始打包..."
echo "这可能需要几分钟时间，请耐心等待..."
pyinstaller pdf_renamer.spec

if [ $? -ne 0 ]; then
    echo
    echo "错误: 打包失败"
    exit 1
fi

echo
echo "5. 检查打包结果..."
if [ -f "dist/PDF合同重命名工具" ]; then
    echo "成功! 可执行文件已生成: dist/PDF合同重命名工具"
    echo
    echo "6. 创建发布文件夹..."
    mkdir -p release
    cp "dist/PDF合同重命名工具" "release/"
    if [ -f "icon.ico" ]; then
        cp "icon.ico" "release/"
    fi
    
    echo
    echo "打包完成! 文件位置:"
    echo "- 可执行文件: release/PDF合同重命名工具"
    echo "- 完整版本: dist/PDF合同重命名工具"
    echo
    echo "注意事项:"
    echo "- 首次运行时EasyOCR会下载模型文件，需要网络连接"
    echo "- 如果启用GPU，确保目标机器有CUDA支持"
    echo "- 建议在目标机器上测试所有功能"
else
    echo "错误: 未找到生成的可执行文件"
    exit 1
fi

echo
echo "完成!"
