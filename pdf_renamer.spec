# -*- mode: python ; coding: utf-8 -*-

import os
import sys
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# 收集EasyOCR的数据文件
easyocr_datas = collect_data_files('easyocr')

# 收集torch和torchvision的数据文件
torch_datas = collect_data_files('torch')
torchvision_datas = collect_data_files('torchvision')

# 收集opencv的数据文件
opencv_datas = collect_data_files('cv2')

# 收集所有相关的隐藏导入
hiddenimports = [
    'easyocr',
    'easyocr.easyocr',
    'easyocr.utils',
    'easyocr.detection',
    'easyocr.recognition',
    'torch',
    'torchvision',
    'cv2',
    'numpy',
    'PIL',
    'PIL._tkinter_finder',
    'tkinter',
    'tkinter.filedialog',
    'tkinter.messagebox',
    'fitz',
    'pymupdf',
    'scipy',
    'scipy.spatial.distance',
    'scipy.special',
    'scipy.special.cython_special',
    'scipy._lib._ccallback_c',
    'scipy._lib._fpumode',
    'scipy.sparse.csgraph._validation',
    'scipy.sparse._matrix',
    'scipy.special._ufuncs_cxx',
    'scipy.linalg._fblas',
    'scipy.linalg._flapack',
    'scipy.linalg.cython_blas',
    'scipy.linalg.cython_lapack',
    'scipy.integrate._odepack',
    'scipy.integrate._quadpack',
    'scipy.integrate._dop',
    'scipy.special._ellip_harm_2',
    'scipy.ndimage._nd_image',
    'scipy.ndimage._ni_support',
    'scipy.spatial.transform._rotation_groups',
    'scipy.spatial.transform',
    'scipy._cyutility',
    'scipy.linalg._cythonized_array_utils',
    'scipy.ndimage',
    'scipy.ndimage._interpolation',
    'scipy.ndimage._ndimage_api',
    'scipy.ndimage._support_alternative_backends',
    'skimage',
    'skimage.measure',
    'skimage.morphology',
    'yaml',
    'bidi',
    'bidi.algorithm',
    'shapely',
    'shapely.geometry',
    'pyclipper',
    'queue',
    'threading',
    'os',
    're',
    'json',
    'urllib',
    'urllib.request',
    'ssl',
    'certifi',
]

# 添加torch和scipy的子模块
hiddenimports.extend(collect_submodules('torch'))
hiddenimports.extend(collect_submodules('torchvision'))
hiddenimports.extend(collect_submodules('scipy'))

# 去重
hiddenimports = list(set(hiddenimports))

block_cipher = None

a = Analysis(
    ['pdf_renamer.py'],
    pathex=[],
    binaries=[],
    datas=easyocr_datas + torch_datas + torchvision_datas + opencv_datas + [
        ('icon.ico', '.'),  # 包含图标文件
    ],
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib',  # 排除不需要的大型库
        'IPython',
        'jupyter',
        'notebook',
        'pandas',
        'seaborn',
        'plotly',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='PDF合同重命名工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico',  # 设置可执行文件图标
)
