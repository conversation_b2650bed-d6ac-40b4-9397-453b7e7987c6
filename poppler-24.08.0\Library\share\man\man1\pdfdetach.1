.\" Copyright 2011 Glyph & Cog, LLC
.TH pdfdetach 1 "15 August 2011"
.SH NAME
pdfdetach \- Portable Document Format (PDF) document embedded file
extractor (version 3.03)
.SH SYNOPSIS
.B pdfdetach
[options]
.RI [ PDF-file ]
.SH DESCRIPTION
.B Pdfdetach
lists or extracts embedded files (attachments) from a Portable
Document Format (PDF) file.
.SH OPTIONS
Some of the following options can be set with configuration file
commands.  These are listed in square brackets with the description of
the corresponding command line option.
.TP
.B \-list
List all of the embedded files in the PDF file.  File names are
converted to the text encoding specified by the "\-enc" switch.
.TP
.BI \-save " number"
Save the specified embedded file.  By default, this uses the file name
associated with the embedded file (as printed by the "\-list" switch);
the file name can be changed with the "\-o" switch.
.TP
.BI \-savefile " filename"
Save the specified embedded file.  By default, this uses the file name
associated with the embedded file (as printed by the "\-list" switch);
the file name can be changed with the "\-o" switch.
.TP
.BI \-saveall
Save all of the embedded files.  This uses the file names associated
with the embedded files (as printed by the "\-list" switch).  By
default, the files are saved in the current directory; this can be
changed with the "\-o" switch.
.TP
.BI \-o " path"
Set the file name used when saving an embedded file with the "\-save"
switch, or the directory used by "\-saveall".
.TP
.BI \-enc " encoding-name"
Sets the encoding to use for text output (embedded file names).
This defaults to "UTF-8".
.TP
.BI \-opw " password"
Specify the owner password for the PDF file.  Providing this will
bypass all security restrictions.
.TP
.BI \-upw " password"
Specify the user password for the PDF file.
.TP
.B \-v
Print copyright and version information.
.TP
.B \-h
Print usage information.
.RB ( \-help
and
.B \-\-help
are equivalent.)
.SH EXIT CODES
The Xpdf tools use the following exit codes:
.TP
0
No error.
.TP
1
Error opening a PDF file.
.TP
2
Error opening an output file.
.TP
3
Error related to PDF permissions.
.TP
99
Other error.
.SH AUTHOR
The pdfinfo software and documentation are copyright 1996-2011 Glyph &
Cog, LLC.
.SH "SEE ALSO"
.BR pdffonts (1),
.BR pdfimages (1),
.BR pdfinfo (1),
.BR pdftocairo (1),
.BR pdftohtml (1),
.BR pdftoppm (1),
.BR pdftops (1),
.BR pdftotext (1)
.BR pdfseparate (1),
.BR pdfsig (1),
.BR pdfunite (1)
