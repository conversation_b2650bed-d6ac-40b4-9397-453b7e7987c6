# 导入必要的库
import os  # 操作系统接口
import re  # 正则表达式
import tkinter as tk  # GUI界面库
from queue import Queue  # 线程安全队列
from tkinter import filedialog, messagebox  # 文件对话框和消息框
from PIL import ImageTk  # PIL图像转Tkinter图像
import fitz  # PyMuPDF库
from PIL import Image  # 图像处理
import easyocr  # OCR识别
import threading  # 多线程

# 为PyInstaller添加隐藏导入
try:
    import scipy._lib._ccallback_c
    import scipy._lib._fpumode
    import scipy.sparse.csgraph._validation
    import scipy.sparse._matrix
    import scipy.special._ufuncs_cxx
    import scipy.linalg._fblas
    import scipy.linalg._flapack
    import scipy.linalg.cython_blas
    import scipy.linalg.cython_lapack
    import scipy.integrate._odepack
    import scipy.integrate._quadpack
    import scipy.integrate._dop
    import scipy.special._ellip_harm_2
    import scipy.ndimage._nd_image
    import scipy.ndimage._ni_support
except ImportError:
    pass  # 在开发环境中这些模块可能不存在

class PDFRenamerGUI:
    """PDF重命名工具的主GUI类"""
    
    def __init__(self, root):
        """初始化GUI界面"""
        self.root = root
        self.root.title("合同文件重命名工具")
        
        # 设置主窗口图标
        self.set_window_icon(self.root)
        
        # 创建日志窗口
        self.log_window = tk.Toplevel(self.root)
        self.log_window.title("处理日志")
        self.log_window.geometry("600x400")
        self.log_window.protocol("WM_DELETE_WINDOW", lambda: None)  # 禁用关闭按钮
        self.set_window_icon(self.log_window)
        
        # 创建带滚动条的日志文本部件
        self.log_text = tk.Text(self.log_window, wrap=tk.WORD)
        scrollbar = tk.Scrollbar(self.log_window, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        # 线程安全的日志队列
        self.log_queue = Queue()
        self.root.after(100, self.process_log_queue)  # 每100ms处理一次日志队列
        
        # 布局滚动条和日志文本
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 默认值设置
        self.pdf_folder = tk.StringVar(value="pdf_files")  # PDF文件夹路径
        self.temp_folder = tk.StringVar(value="temp_images")  # 临时文件夹路径
        self.crop_area = (0, 0, 2338, 1654)  # 默认裁剪区域 (左,上,右,下)
        self.regex_pattern = tk.StringVar(value=r'(?:MO|MPSO|M0|MPS0)\d{11}')  # 默认合同编号正则表达式，包含可能的0识别错误
        self.use_gpu = tk.BooleanVar(value=False)  # GPU使用选项
        self.model_path = tk.StringVar(value="")  # 本地模型路径
        self.use_local_model = tk.BooleanVar(value=False)  # 是否使用本地模型
        self.selection_start = None  # 区域选择起始坐标
        self.selection_rect = None  # 区域选择矩形对象

        # 初始化EasyOCR读取器（延迟初始化，在需要时才创建）
        self.ocr_reader = None
        
        # Create GUI elements
        tk.Label(root, text="合同文件夹:").grid(row=0, column=0, sticky="w")
        tk.Entry(root, textvariable=self.pdf_folder, width=40).grid(row=0, column=1)
        tk.Button(root, text="浏览", command=self.browse_pdf_folder).grid(row=0, column=2)

        tk.Label(root, text="TEMP文件夹:").grid(row=1, column=0, sticky="w")
        tk.Entry(root, textvariable=self.temp_folder, width=40).grid(row=1, column=1)
        tk.Button(root, text="浏览", command=self.browse_temp_folder).grid(row=1, column=2)

        tk.Label(root, text="正则表达式:").grid(row=2, column=0, sticky="w")
        tk.Entry(root, textvariable=self.regex_pattern, width=40).grid(row=2, column=1)

        # 本地模型选项
        tk.Label(root, text="本地模型:").grid(row=3, column=0, sticky="w")
        model_frame = tk.Frame(root)
        model_frame.grid(row=3, column=1, sticky="w")
        tk.Checkbutton(model_frame, text="使用本地模型", variable=self.use_local_model,
                      command=self.toggle_model_path).pack(side=tk.LEFT)

        tk.Label(root, text="模型路径:").grid(row=4, column=0, sticky="w")
        self.model_entry = tk.Entry(root, textvariable=self.model_path, width=40, state=tk.DISABLED)
        self.model_entry.grid(row=4, column=1)
        self.model_browse_btn = tk.Button(root, text="浏览", command=self.browse_model_folder, state=tk.DISABLED)
        self.model_browse_btn.grid(row=4, column=2)

        # GPU选项
        tk.Label(root, text="使用GPU加速:").grid(row=5, column=0, sticky="w")
        gpu_frame = tk.Frame(root)
        gpu_frame.grid(row=5, column=1, sticky="w")
        tk.Checkbutton(gpu_frame, text="启用GPU加速 (需要CUDA支持)", variable=self.use_gpu,
                      command=self.on_settings_changed).pack(side=tk.LEFT)

        self.process_btn = tk.Button(root, text="处理PDF文件", command=self.start_processing, bg="green", fg="white")
        self.process_btn.grid(row=6, column=2, pady=5)

        # 添加帮助按钮
        help_btn = tk.Button(root, text="使用帮助", command=self.show_model_help, bg="lightblue")
        help_btn.grid(row=6, column=1, pady=2)

    def init_ocr_reader(self):
        """初始化EasyOCR读取器"""
        try:
            import os  # 在函数开始就导入os模块

            # 检查GPU可用性
            gpu_available = False
            if self.use_gpu.get():
                try:
                    import torch
                    gpu_available = torch.cuda.is_available()
                    if not gpu_available:
                        self.log_message("警告: 已启用GPU选项但CUDA不可用，将使用CPU")
                except ImportError:
                    self.log_message("警告: 已启用GPU选项但PyTorch未安装，将使用CPU")

            # 确定模型存储目录
            model_storage_dir = None
            if self.use_local_model.get() and self.model_path.get().strip():
                model_storage_dir = self.model_path.get().strip()
                if not os.path.exists(model_storage_dir):
                    self.log_message(f"警告: 指定的模型路径不存在: {model_storage_dir}")
                    self.log_message("将使用默认模型路径")
                    model_storage_dir = None
                else:
                    self.log_message(f"使用本地模型路径: {model_storage_dir}")

            # 检查是否为首次运行（仅在使用默认路径时）
            is_first_run = False
            if model_storage_dir is None:
                default_model_dir = os.path.expanduser("~/.EasyOCR/model")
                is_first_run = not os.path.exists(default_model_dir) or len(os.listdir(default_model_dir) if os.path.exists(default_model_dir) else []) == 0

                if is_first_run:
                    self.log_message("首次运行检测到，正在下载OCR模型文件...")
                    self.log_message("这可能需要几分钟时间，请耐心等待...")
                    self.log_message("模型文件大小约30-50MB，下载完成后将保存到本地")

            # 初始化EasyOCR，支持英文和中文
            use_gpu = self.use_gpu.get() and gpu_available

            # 根据是否指定本地模型路径来初始化
            if model_storage_dir:
                self.ocr_reader = easyocr.Reader(['en', 'ch_sim'], gpu=use_gpu,
                                                model_storage_directory=model_storage_dir,
                                                download_enabled=False)  # 禁用下载，只使用本地模型
            else:
                self.ocr_reader = easyocr.Reader(['en', 'ch_sim'], gpu=use_gpu)

            if use_gpu:
                self.log_message("EasyOCR初始化成功 (GPU加速)")
            else:
                self.log_message("EasyOCR初始化成功 (CPU模式)")

            if is_first_run:
                self.log_message("模型下载完成！后续运行将直接使用本地模型文件")

        except Exception as e:
            self.log_message(f"EasyOCR初始化失败: {str(e)}")
            if model_storage_dir:
                self.log_message("提示: 请检查本地模型路径是否正确，或尝试使用默认路径")
            self.ocr_reader = None

    def correct_ocr_text(self, text):
        """修正OCR识别中的常见错误，特别是数字0被误识别为字母O的情况"""
        if not text:
            return text

        # 修正常见的OCR错误：将特定位置的数字0转换为字母O
        # MPS0 -> MPSO
        text = re.sub(r'\bMPS0\b', 'MPSO', text)
        # M0 -> MO (但要确保不是在数字序列中)
        text = re.sub(r'\bM0(?=\d)', 'MO', text)  # M0后面跟数字的情况
        text = re.sub(r'\bM0\b', 'MO', text)  # 独立的M0

        return text

    def set_window_icon(self, window):
        """为指定窗口设置图标"""
        icon_paths = ['icon.ico', 'icon.png', 'icon.jpg']  # 可能的图标文件路径
        for icon_path in icon_paths:
            if os.path.exists(icon_path):
                try:
                    window.iconbitmap(icon_path)  # 尝试设置.ico图标
                    break
                except:
                    try:
                        img = tk.PhotoImage(file=icon_path)  # 尝试加载其他格式图片
                        window.tk.call('wm', 'iconphoto', window._w, img)
                        break
                    except:
                        continue  # 如果都不支持则继续尝试下一个
    
    def process_log_queue(self):
        """处理日志队列中的消息"""
        while not self.log_queue.empty():
            try:
                message = self.log_queue.get_nowait()  # 非阻塞获取消息
                self.log_text.insert(tk.END, message + "\n")  # 插入日志
                self.log_text.see(tk.END)  # 滚动到最新
                self.log_text.update()  # 更新显示
            except:
                break  # 队列为空时退出
        self.root.after(100, self.process_log_queue)  # 100ms后再次检查
    
    def log_message(self, message):
        """通过线程安全队列添加日志消息"""
        self.log_queue.put(message)  # 线程安全地添加消息
    
    
    def browse_pdf_folder(self):
        """浏览并选择PDF文件夹"""
        folder = filedialog.askdirectory()  # 打开文件夹选择对话框
        if folder:
            self.pdf_folder.set(folder)  # 设置选择的文件夹路径

    def browse_temp_folder(self):
        """浏览并选择临时文件夹"""
        folder = filedialog.askdirectory()  # 打开文件夹选择对话框
        if folder:
            self.temp_folder.set(folder)  # 设置选择的临时文件夹路径

    def browse_model_folder(self):
        """浏览并选择模型文件夹"""
        folder = filedialog.askdirectory(title="选择EasyOCR模型文件夹")
        if folder:
            self.model_path.set(folder)

    def toggle_model_path(self):
        """切换本地模型路径输入框的状态"""
        if self.use_local_model.get():
            self.model_entry.config(state=tk.NORMAL)
            self.model_browse_btn.config(state=tk.NORMAL)
        else:
            self.model_entry.config(state=tk.DISABLED)
            self.model_browse_btn.config(state=tk.DISABLED)
        # 设置更改时清除OCR读取器
        self.on_settings_changed()

    def on_settings_changed(self):
        """当GPU或模型设置更改时调用，清除现有的OCR读取器"""
        if hasattr(self, 'ocr_reader') and self.ocr_reader is not None:
            self.ocr_reader = None
            self.log_message("设置已更改，将在下次处理时重新初始化OCR")

    def show_model_help(self):
        """显示帮助信息"""
        help_text = """使用说明：

1.勾选使用本地模型（因为模型在国外服务器，我给他下载了），选择model文件夹。

2.选择需要重命名的PDF文件夹（点击开始处理后会把文件夹下所有的PDF的第一张都扫一遍）

3.点击绿色按钮开始处理，会弹出个窗口，框选合同编号区域。点击确认后会自动重命名PDF文件。
   
tips:temp文件夹是用来放裁切出来的图片的，没什么用可填可不填，不填会在当前目录下生成一个temp文件夹。

注意：使用本地模型时将禁用自动下载功能"""

        messagebox.showinfo("模型使用帮助", help_text)
    
    def start_processing(self):
        """开始PDF处理流程，首先显示第一个PDF用于区域选择"""
        pdf_folder = self.pdf_folder.get()  # 获取PDF文件夹路径
        if not os.path.exists(pdf_folder) or not os.listdir(pdf_folder):
            messagebox.showerror("错误", "PDF文件夹为空或不存在!")  # 错误提示
            return
            
        # 查找第一个PDF文件
        for filename in os.listdir(pdf_folder):
            if filename.lower().endswith('.pdf'):
                pdf_path = os.path.join(pdf_folder, filename)
                break
        else:
            messagebox.showerror("错误", "没有找到PDF文件!")  # 错误提示
            return
            
        # 将第一页转换为高分辨率图像获取实际尺寸
        try:
            doc = fitz.open(pdf_path)
            page = doc.load_page(0)
            
            # 首先生成高分辨率图像获取实际尺寸
            high_res_pix = page.get_pixmap(dpi=300)
            self.high_res_size = (high_res_pix.width, high_res_pix.height)
            
            # 然后生成预览图像
            preview_pix = page.get_pixmap()
            preview_img = Image.frombytes("RGB", [preview_pix.width, preview_pix.height], preview_pix.samples)
            
            # 显示选择窗口并传入预览图像
            if hasattr(self, 'is_retry'):
                del self.is_retry  # 清除重试标志(如果是初始选择)
            self.show_selection_window(preview_img)  # 显示选择窗口
            
        except Exception as e:
            messagebox.showerror("错误", f"无法显示PDF: {str(e)}")  # 错误提示
            self.process_btn.config(state=tk.NORMAL)  # 恢复按钮状态
    
    def on_selection_start(self, event):
        """开始区域选择(鼠标按下事件)"""
        self.selection_start = (event.x, event.y)  # 记录起始坐标
        if self.selection_rect:
            self.canvas.delete(self.selection_rect)  # 清除之前的选区
    
    def on_selection_drag(self, event):
        """更新选择区域(鼠标拖动事件)"""
        if not self.selection_start:
            return  # 如果没有起始点则返回
            
        if self.selection_rect:
            self.canvas.delete(self.selection_rect)  # 删除旧的选区
            
        x0, y0 = self.selection_start  # 起始坐标
        x1, y1 = event.x, event.y  # 当前坐标
        # 创建红色矩形选区
        self.selection_rect = self.canvas.create_rectangle(x0, y0, x1, y1,
                                                         outline='red', width=2)
    
    def on_selection_end(self, event):
        """完成选择(鼠标释放事件)"""
        if not self.selection_start:
            return  # 如果没有起始点则返回
            
        x0, y0 = self.selection_start  # 起始坐标
        x1, y1 = event.x, event.y  # 结束坐标
        # 将预览图坐标转换回高分辨率图像坐标
        orig_x0 = int(x0 * self.preview_scale_x)
        orig_y0 = int(y0 * self.preview_scale_y)
        orig_x1 = int(x1 * self.preview_scale_x)
        orig_y1 = int(y1 * self.preview_scale_y)
        print (orig_x0,orig_y0,orig_x1,orig_y1)
        print(self.preview_scale_x,self.preview_scale_y)
        # 设置裁剪区域(确保左上和右下坐标正确)
        self.crop_area = (min(orig_x0, orig_x1), min(orig_y0, orig_y1),
                         max(orig_x0, orig_x1), max(orig_y0, orig_y1))
    
    def confirm_selection(self):
        """确认选择的区域"""
        if not hasattr(self, 'crop_area') or len(self.crop_area) != 4:
            messagebox.showerror("错误", "请先选择合同编号区域!")  # 错误提示
            return
            
        self.selection_window.destroy()  # 关闭选择窗口
        
        # 只有初始选择时才启动处理流程(不是重试)
        if not hasattr(self, 'is_retry'):
            self.process_btn.config(state=tk.DISABLED, text="处理中...")  # 禁用按钮
            thread = threading.Thread(target=self.process_pdfs)  # 创建处理线程
            thread.start()  # 启动线程
        
    def process_pdfs(self):
        """处理所有PDF文件的主方法"""
        try:
            # 从GUI输入设置全局变量
            PDF_FOLDER = self.pdf_folder.get()  # 获取PDF文件夹路径
            TEMP_FOLDER = self.temp_folder.get()  # 获取临时文件夹路径

            # 在开始处理前初始化OCR读取器（只初始化一次）
            self.log_message("正在初始化OCR读取器...")
            self.init_ocr_reader()
            if self.ocr_reader is None:
                self.log_message("OCR读取器初始化失败，无法继续处理")
                self.root.after(0, lambda: messagebox.showerror("错误", "OCR读取器初始化失败"))
                return

            # 处理每个PDF文件
            for filename in os.listdir(PDF_FOLDER):
                if not filename.lower().endswith('.pdf'):
                    continue  # 跳过非PDF文件
                    
                pdf_path = os.path.join(PDF_FOLDER, filename)
                while True:  # 当前文件的重试循环
                    try:
                        # 使用当前裁剪区域处理
                        code = self.process_single_pdf(pdf_path, TEMP_FOLDER)
                        
                        if code:
                            # 如果提取到编号则重命名文件
                            new_name = f"{code}.pdf"
                            new_path = os.path.join(PDF_FOLDER, new_name)
                            os.rename(pdf_path, new_path)
                            self.log_message(f"重命名 {filename} 为 {new_name}")
                            break
                        else:
                            # 询问用户如何处理识别失败
                            choice = self.handle_failed_recognition(pdf_path)
                            if choice == "retry":
                                continue  # 重新尝试
                            elif choice == "skip":
                                self.log_message(f"跳过 {filename}")
                                break  # 跳过当前文件
                            
                    except Exception as e:
                        # 显示错误信息
                        self.root.after(0, lambda f=filename, e=e: messagebox.showerror("错误", f"处理{f}时发生错误: {str(e)}"))
                        break
                        
            # 处理完成提示
            self.root.after(0, lambda: messagebox.showinfo("完成", "PDF文件处理完成!"))
        except Exception as e:
            # 显示全局错误
            self.root.after(0, lambda e=e: messagebox.showerror("错误", f"发生错误: {str(e)}"))
        finally:
            # 恢复按钮状态
            self.root.after(0, lambda: self.process_btn.config(state=tk.NORMAL, text="处理PDF文件"))
    
    def process_single_pdf(self, pdf_path, temp_folder):
        """处理单个PDF文件并返回合同编号"""
        # 将PDF转换为高分辨率图像
        doc = fitz.open(pdf_path)
        page = doc.load_page(0)
        # 生成高分辨率图像(300 DPI)
        high_res_pix = page.get_pixmap(dpi=300)
        high_res_img = Image.frombytes("RGB", [high_res_pix.width, high_res_pix.height], high_res_pix.samples)
        
        # 确保临时目录存在
        ensure_dir(temp_folder)
        
        # 保存高分辨率原图
        high_res_path = os.path.join(temp_folder, "temp_high_res.jpg")
        high_res_img.save(high_res_path, 'JPEG', quality=95)
        
        # 直接使用已缩放过的裁剪区域
        scaled_crop = self.crop_area
        
        # 确保裁剪区域在图像范围内
        width, height = high_res_img.size
        scaled_crop = (
            max(0, min(scaled_crop[0], width-1)),
            max(0, min(scaled_crop[1], height-1)),
            max(0, min(scaled_crop[2], width)),
            max(0, min(scaled_crop[3], height))
        )
        
        # 记录调试信息
        #self.log_message(f"裁剪区域: {scaled_crop}, 图像尺寸: {width}x{height}")
        
        # 从高分辨率图中裁剪
        cropped = high_res_img.crop(scaled_crop)
        
        # 保存裁剪后的高分辨率图像
        temp_img_path = os.path.join(temp_folder, "temp_crop_high_res.jpg")
        cropped.save(temp_img_path, 'JPEG', quality=95)

        try:
            # 使用EasyOCR进行文字识别（OCR读取器已在process_pdfs中初始化）
            results = self.ocr_reader.readtext(temp_img_path)

            # 提取所有识别到的文字
            text_parts = []
            for (bbox, text, confidence) in results:
                print(text,confidence)
                if confidence > 0.02:  # 降低置信度阈值到0.3
                    text_parts.append(text)

            # 合并所有文字
            text = ' '.join(text_parts)
            print(f"识别到的原始文字: {text}")
            self.log_message(f"识别到的原始文字: {text}")

            # 修正OCR识别错误
            corrected_text = self.correct_ocr_text(text)
            print(f"修正后的文字: {corrected_text}")
            
            self.log_message(f"修正后的文字: {corrected_text}")

            # 使用正则表达式匹配合同编号
            match = re.search(self.regex_pattern.get(), corrected_text)
            if match:
                matched_text = match.group()
                # 对匹配到的文本再次应用修正
                final_text = self.correct_ocr_text(matched_text)
                print(f"最终合同编号: {final_text}")
                self.log_message(f"最终合同编号: {final_text}")
                return final_text
            return None  # 返回匹配结果或None
        except Exception as e:
            self.log_message(f"OCR错误: {str(e)}")  # 记录OCR错误
            return None
    
    def show_selection_window(self, image):
        """显示图像选择窗口"""
        self.selection_window = tk.Toplevel(self.root)
        self.selection_window.title("选择合同编号区域")
        
        # 保存预览图像
        self.preview_img = image
        
        # 创建低分辨率预览图(保持宽高比)
        preview_img = image.copy()
        max_size = (800, 600)
        preview_img.thumbnail(max_size, Image.LANCZOS)
        self.preview_width, self.preview_height = preview_img.size
        self.selection_img = preview_img
        
        # 计算预览图到高分辨率图的缩放比例
        self.preview_scale_x = self.high_res_size[0] / self.preview_width
        self.preview_scale_y = self.high_res_size[1] / self.preview_height
        # self.log_message(f"缩放比例: X={self.preview_scale_x}, Y={self.preview_scale_y}")
        
        # 计算缩放因子以适应窗口(最大800x600)
        max_width, max_height = 800, 600
        width_ratio = max_width / self.preview_width
        height_ratio = max_height / self.preview_height
        self.scale_factor = min(width_ratio, height_ratio)
        
        # 计算缩放后的尺寸
        scaled_width = self.preview_width * self.scale_factor
        scaled_height = self.preview_height * self.scale_factor
        
        # 使用低分辨率预览图
        self.tk_img = ImageTk.PhotoImage(self.selection_img)
        
        # 创建带滚动条的画布
        frame = tk.Frame(self.selection_window)
        frame.pack(fill=tk.BOTH, expand=True)
        
        canvas = tk.Canvas(frame, width=scaled_width, height=scaled_height)
        canvas.create_image(0, 0, anchor=tk.NW, image=self.tk_img)  # 显示图像
        canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 保存画布引用
        self.canvas = canvas
        
        # 绑定鼠标事件用于选择区域
        self.canvas.bind("<ButtonPress-1>", self.on_selection_start)  # 鼠标按下
        self.canvas.bind("<B1-Motion>", self.on_selection_drag)  # 鼠标拖动
        self.canvas.bind("<ButtonRelease-1>", self.on_selection_end)  # 鼠标释放
        
        # 在底部添加确认按钮
        btn_frame = tk.Frame(self.selection_window)
        btn_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=10)
        
        confirm_btn = tk.Button(btn_frame, text="确认选择",
                              command=self.confirm_selection,
                              width=15, height=2)
        confirm_btn.pack()  # 布局确认按钮
    
    def handle_failed_recognition(self, pdf_path):
        """Show dialog for failed recognition and return user choice"""
        self.is_retry = True  # Mark as retry operation
        result = {"choice": None, "done": False}
        
        def on_retry():
            nonlocal result
            result["choice"] = "retry"
            dialog.destroy()
            
            # Convert PDF to image and show selection window
            try:
                doc = fitz.open(pdf_path)
                page = doc.load_page(0)
                pix = page.get_pixmap()
                img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)
                self.show_selection_window(img)
                # Wait for user to confirm new selection
                self.selection_window.wait_window()
                result["done"] = True
            except Exception as e:
                messagebox.showerror("错误", f"无法显示PDF: {str(e)}")
                result["choice"] = "skip"
                result["done"] = True
            
        def on_skip():
            nonlocal result
            result["choice"] = "skip"
            result["done"] = True
            dialog.destroy()
            
        dialog = tk.Toplevel(self.root)
        dialog.title("识别失败")
        dialog.resizable(False, False)
        
        tk.Label(dialog, text=f"无法识别文件中的合同编号:\n{os.path.basename(pdf_path)}",
               padx=20, pady=10).pack()
               
        btn_frame = tk.Frame(dialog)
        btn_frame.pack(pady=10)
        
        tk.Button(btn_frame, text="重新选择区域", command=on_retry,
                width=15).pack(side=tk.LEFT, padx=5)
        tk.Button(btn_frame, text="跳过此文件", command=on_skip,
                width=15).pack(side=tk.LEFT, padx=5)
        
        # Wait for user response
        dialog.transient(self.root)
        dialog.grab_set()
        self.root.wait_window(dialog)
        
        # Wait for selection to complete if retry was chosen
        while result["choice"] == "retry" and not result["done"]:
            self.root.update()
            
        return result["choice"]

def ensure_dir(directory):
    """确保目录存在，不存在则创建"""
    if not os.path.exists(directory):
        os.makedirs(directory)  # 递归创建目录

if __name__ == "__main__":
    root = tk.Tk()
    app = PDFRenamerGUI(root)
    root.mainloop()
